// WebSocket连接和数据管理
class PositionVisualizer {
    constructor() {
        this.socket = null;
        this.positions = {};
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.updateConnectionStatus('连接中...');
    }

    connectWebSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.updateConnectionStatus('已连接');
        });

        this.socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            this.updateConnectionStatus('连接断开');
        });

        this.socket.on('initial_data', (data) => {
            console.log('收到初始数据:', data);
            this.positions = data;
            this.renderAllPositions();
        });

        this.socket.on('position_update', (update) => {
            console.log('收到持仓更新:', update);
            this.updatePosition(update);
            this.updateLastUpdateTime();
        });
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        statusElement.textContent = status;

        if (status === '已连接') {
            statusElement.className = 'status-connected';
        } else {
            statusElement.className = 'status-disconnected';
        }
    }

    updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN');
        document.getElementById('last-update').textContent = `最后更新: ${timeString}`;
    }

    updatePosition(update) {
        const { exchange, symbol, position_side, data } = update;

        // 更新内存数据
        if (!this.positions[exchange]) {
            this.positions[exchange] = {};
        }
        if (!this.positions[exchange][symbol]) {
            this.positions[exchange][symbol] = {};
        }
        this.positions[exchange][symbol][position_side] = data;

        // 重新渲染
        this.renderAllPositions();
    }

    renderAllPositions() {
        const container = document.getElementById('exchanges-container');

        if (Object.keys(this.positions).length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <p>等待交易所数据...</p>
                    <p class="hint">请向 <code>/api/position</code> 发送POST请求上报持仓数据</p>
                </div>
            `;
            return;
        }

        let html = '';

        for (const [exchange, symbols] of Object.entries(this.positions)) {
            for (const [symbol, positions] of Object.entries(symbols)) {
                html += this.renderExchangeCard(exchange, symbol, positions);
            }
        }

        container.innerHTML = html;
    }

    renderExchangeCard(exchange, symbol, positions) {
        const longPos = positions.LONG || null;
        const shortPos = positions.SHORT || null;

        return `
            <div class="exchange-card">
                <div class="exchange-header">
                    <div class="exchange-name">${exchange}</div>
                    <div class="symbol-info">${symbol}</div>
                </div>
                <div class="positions-container">
                    ${this.renderPositionBar('LONG', longPos)}
                    ${this.renderPositionBar('SHORT', shortPos)}
                </div>
            </div>
        `;
    }

    renderPositionBar(side, position) {
        const sideClass = side.toLowerCase();

        if (!position) {
            return `
                <div class="position-bar">
                    <div class="position-header">
                        <span class="position-side ${sideClass}">${side}</span>
                        <span class="position-info">无持仓</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar neutral" style="width: 0%">
                            无持仓
                        </div>
                    </div>
                </div>
            `;
        }

        const progress = Math.min(position.progress || 0, 100);
        const pnl = position.pnl || 0;
        const isProfit = position.is_profit || false;
        const colorClass = pnl === 0 ? 'neutral' : (isProfit ? 'profit' : 'loss');

        const currentQuantity = position.current_quantity || 0;
        const openQuantity = position.open_quantity || 0;
        const openAvgPrice = position.open_avg_price || 0;
        const openingDuration = this.formatDuration(position.opening_duration || 0);
        const holdingDuration = this.formatDuration(position.holding_duration || 0);
        const closingDuration = this.formatDuration(position.closing_duration || 0);
        const openTime = position.open_time || '';
        const openStartTime = position.open_start_time || '';
        const position95Time = position.position_95_time || '';
        const positionBelow95Time = position.position_below_95_time || '';
        const position5Time = position.position_5_time || '';

        return `
            <div class="position-bar">
                <div class="position-header">
                    <span class="position-side ${sideClass}">${side}</span>
                    <span class="position-info">
                        ${currentQuantity}/${openQuantity} (${progress.toFixed(1)}%)
                    </span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar ${colorClass}" style="width: ${progress}%">
                        ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)} USDT
                    </div>
                </div>
                <div class="position-details">
                    <div class="detail-item">
                        <div class="detail-label">开仓价格</div>
                        <div class="detail-value">${parseFloat(openAvgPrice).toFixed(2)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">开仓耗时</div>
                        <div class="detail-value">${openingDuration}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">持仓时间</div>
                        <div class="detail-value">${holdingDuration}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">清仓耗时</div>
                        <div class="detail-value">${closingDuration}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">浮动盈亏</div>
                        <div class="detail-value" style="color: ${isProfit ? '#e74c3c' : '#27ae60'}">
                            ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)}
                        </div>
                    </div>
                </div>
                ${this.renderTimeDetails(openStartTime, position95Time, positionBelow95Time, position5Time)}
            </div>
        `;
    }

    renderTimeDetails(openStartTime, position95Time, positionBelow95Time, position5Time) {
        if (!openStartTime && !position95Time && !positionBelow95Time && !position5Time) {
            return '';
        }

        return `
            <div class="time-details">
                <div class="time-details-header">时间节点</div>
                <div class="time-details-content">
                    ${openStartTime ? `<div class="time-item"><span class="time-label">开仓开始:</span> ${openStartTime}</div>` : ''}
                    ${position95Time ? `<div class="time-item"><span class="time-label">达到95%:</span> ${position95Time}</div>` : ''}
                    ${positionBelow95Time ? `<div class="time-item"><span class="time-label">降到95%以下:</span> ${positionBelow95Time}</div>` : ''}
                    ${position5Time ? `<div class="time-item"><span class="time-label">降到5%:</span> ${position5Time}</div>` : ''}
                </div>
            </div>
        `;
    }

    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds}秒`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            return `${minutes}分钟`;
        } else if (seconds < 86400) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}小时${minutes}分钟`;
        } else {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            return `${days}天${hours}小时`;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PositionVisualizer();
});
