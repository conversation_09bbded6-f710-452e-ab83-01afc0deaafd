/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

#connection-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-connected {
    background: #27ae60;
    color: white;
}

.status-disconnected {
    background: #e74c3c;
    color: white;
}

#last-update {
    font-size: 12px;
    color: #7f8c8d;
}

/* 主内容区域 */
main {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-data p {
    margin-bottom: 10px;
    font-size: 16px;
}

.no-data .hint {
    font-size: 14px;
    font-style: italic;
}

.no-data code {
    background: #ecf0f1;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

/* 交易所卡片样式 */
.exchange-card {
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.exchange-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e8ed;
}

.exchange-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
}

.symbol-info {
    font-size: 14px;
    color: #7f8c8d;
    margin-top: 2px;
}

.positions-container {
    padding: 20px;
}

/* 持仓条样式 */
.position-bar {
    margin-bottom: 20px;
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.position-side {
    font-weight: 600;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
}

.position-side.long {
    background: #27ae60;
}

.position-side.short {
    background: #e74c3c;
}

.position-info {
    font-size: 12px;
    color: #7f8c8d;
}

.progress-container {
    background: #ecf0f1;
    border-radius: 10px;
    height: 30px;
    position: relative;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 12px;
}

.progress-bar.profit {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.progress-bar.loss {
    background: linear-gradient(90deg, #27ae60, #229954);
}

.progress-bar.neutral {
    background: linear-gradient(90deg, #95a5a6, #7f8c8d);
}

.position-details {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #7f8c8d;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.detail-label {
    font-weight: 500;
    margin-bottom: 2px;
}

.detail-value {
    color: #2c3e50;
    font-weight: 600;
}

/* 时间详情样式 */
.time-details {
    margin-top: 10px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #3498db;
}

.time-details-header {
    font-size: 11px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.time-details-content {
    font-size: 10px;
    color: #7f8c8d;
}

.time-item {
    margin-bottom: 2px;
}

.time-label {
    font-weight: 500;
    color: #34495e;
}

/* 底部图例 */
footer {
    margin-top: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #2c3e50;
}

.color-box {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.color-box.profit {
    background: #e74c3c;
}

.color-box.loss {
    background: #27ae60;
}

.color-box.neutral {
    background: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .status {
        align-items: center;
    }

    .position-details {
        flex-wrap: wrap;
        gap: 10px;
    }

    .legend {
        gap: 15px;
    }
}
